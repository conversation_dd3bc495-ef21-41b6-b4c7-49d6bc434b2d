# DDEV n8n recipe file.
#ddev-generated

services:
  n8n:
    container_name: ddev-${DDEV_SITENAME}-n8n
    image: ghcr.io/n8n-io/n8n:${N8N_TAG:-latest}
    networks: [default, ddev_default]
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT
    restart: "no"
    user: "$DDEV_UID:$DDEV_GID"
    expose:
      - "5678"
    volumes:
      - ".:/mnt/ddev_config"
      - ./n8n_data:/data
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      GENERIC_TIMEZONE: "${N8N_TIMEZONE:-Europe/Berlin}"
      N8N_BASIC_AUTH_ACTIVE: "${N8N_BASIC_AUTH_ACTIVE:-false}"
      N8N_BASIC_AUTH_USER: "${N8N_BASIC_AUTH_USER:-n8n}"
      N8N_BASIC_AUTH_PASSWORD: "${N8N_BASIC_AUTH_PASSWORD:-n8n}"
      N8N_HOST: "${DDEV_SITENAME}.ddev.site"
      N8N_PORT: 5678
      N8N_PROTOCOL: http
      N8N_ENCRYPTION_KEY: "${N8N_ENCRYPTION_KEY:-n8n}"
      N8N_USER_FOLDER: "/mnt/ddev_config/n8n/data"
      #N8N_LOG_LEVEL: debug
      N8N_LOG_OUTPUT: "stdout"
      VIRTUAL_HOST: "$DDEV_HOSTNAME"
      HTTP_EXPOSE: "5678:5678"
      HTTPS_EXPOSE: "5679:5678"
